package com.czb.hn.service.business.impl;

import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.dto.alert.AlertSearchCriteriaDto;
import com.czb.hn.dto.alert.AlertSearchResultDto;
import com.czb.hn.dto.PlanDTO;
import com.czb.hn.dto.alert.AlertStatisticsDto;
import com.czb.hn.dto.workbench.AlertListItemDTO;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.czb.hn.jpa.securadar.repository.AlertResultRepository;
import com.czb.hn.service.business.AlertSearchService;
import com.czb.hn.service.business.PlanService;

import com.czb.hn.util.AlertResultMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Alert Search Service Implementation
 * 预警信息搜索服务实现，基于MySQL提供高性能搜索功能
 */
@Service
@Slf4j
public class AlertSearchServiceImpl implements AlertSearchService {

    @Autowired
    private AlertResultRepository alertResultRepository;

    @Autowired
    private AlertResultMapper alertResultMapper;

    @Autowired
    private PlanService planService;

    @Override
    public AlertSearchResultDto searchAlerts(AlertSearchCriteriaDto criteria) {
        log.info("Executing alert search with criteria: {}", criteria);

        try {
            // Create pageable with sorting by warning time descending (newest first)
            Pageable pageable = PageRequest.of(criteria.page() - 1, criteria.size());

            Integer informationSensitivityTypeValue = criteria.informationSensitivityType();
            String warningLevel = criteria.warningLevel();
            String sourceType = criteria.sourceType();

            // Execute search query
            Page<AlertResult> resultPage = alertResultRepository.findWithFiltersAndFullTextSearch(
                    criteria.planId(),
                    informationSensitivityTypeValue,
                    warningLevel,
                    sourceType,
                    criteria.startTime(),
                    criteria.endTime(),
                    criteria.searchText(),
                    pageable);

            // Convert entities to DTOs
            List<AlertResultResponseDto> content = resultPage.getContent().stream()
                    .map(alertResultMapper::toResponseDto)
                    .collect(Collectors.toList());

            // Create page info
            AlertSearchResultDto.PageInfo pageInfo = new AlertSearchResultDto.PageInfo(
                    resultPage.getNumber(),
                    resultPage.getSize(),
                    resultPage.getTotalPages(),
                    resultPage.getTotalElements(),
                    resultPage.isFirst(),
                    resultPage.isLast(),
                    resultPage.hasNext(),
                    resultPage.hasPrevious());

            // Create and return search result
            return new AlertSearchResultDto(content, pageInfo, criteria);

        } catch (Exception e) {
            log.error("Error executing alert search: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to execute alert search", e);
        }
    }

    @Override
    public AlertStatisticsDto getAlertStatistics(Long planId, LocalDateTime startTime, LocalDateTime endTime) {
        log.info("Getting alert statistics for planId={}, startTime={}, endTime={}", planId, startTime, endTime);

        try {
            // 验证方案是否存在并获取方案信息
            PlanDTO planDto = planService.getPlanById(planId);
            String planName = planDto.name();

            // 获取总预警条数
            Long totalAlertCount = alertResultRepository.countByPlanIdAndWarningTimeBetween(planId, startTime, endTime);

            // 获取敏感信息类型预警条数
            Long sensitiveAlertCount = alertResultRepository.countSensitiveByPlanIdAndWarningTimeBetween(planId,
                    startTime, endTime);

            log.info("Alert statistics for plan {}: total={}, sensitive={}", planName, totalAlertCount,
                    sensitiveAlertCount);

            return new AlertStatisticsDto(
                    planId,
                    planName,
                    startTime,
                    endTime,
                    totalAlertCount,
                    sensitiveAlertCount);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid plan ID: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error getting alert statistics for planId {}: {}", planId, e.getMessage(), e);
            throw new RuntimeException("Failed to get alert statistics", e);
        }
    }

    @Override
    public List<AlertListItemDTO> getAlertListByPlanId(Long planId, Integer limit) {
        try {
            log.info("Getting alert list for plan: {}, limit: {}", planId, limit);

            // 验证参数
            if (planId == null) {
                throw new IllegalArgumentException("Plan ID cannot be null");
            }

            // 设置默认限制
            int actualLimit = (limit != null && limit > 0) ? limit : 20;

            // 验证限制范围
            if (actualLimit > 100) {
                log.warn("Limit {} exceeds maximum allowed (100), using 100 instead", actualLimit);
                actualLimit = 100;
            }

            // 创建分页参数
            Pageable pageable = PageRequest.of(0, actualLimit);

            // 查询预警信息
            List<AlertResult> alertResults = alertResultRepository.findTopAlertsByPlanId(planId, pageable);

            // 转换为DTO
            List<AlertListItemDTO> alertList = alertResults.stream()
                    .map(AlertListItemDTO::fromEntity)
                    .collect(Collectors.toList());

            log.info("Retrieved {} alerts for plan: {}", alertList.size(), planId);
            return alertList;

        } catch (Exception e) {
            log.error("Error getting alert list for plan {}: {}", planId, e.getMessage(), e);
            throw new RuntimeException("Failed to get alert list: " + e.getMessage(), e);
        }
    }

    @Override
    public List<AlertListItemDTO> getAlertListByPlanIdAndTimeRange(Long planId, LocalDateTime startTime,
            LocalDateTime endTime, Integer limit) {
        try {
            log.info("Getting alert list for plan: {}, startTime: {}, endTime: {}, limit: {}", planId, startTime,
                    endTime, limit);

            // 验证参数
            if (planId == null) {
                throw new IllegalArgumentException("Plan ID cannot be null");
            }

            // 验证时间范围
            if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
                throw new IllegalArgumentException("Start time cannot be after end time");
            }

            // 设置默认限制
            int actualLimit = (limit != null && limit > 0) ? limit : 20;

            // 验证限制范围
            if (actualLimit > 100) {
                log.warn("Limit {} exceeds maximum allowed (100), using 100 instead", actualLimit);
                actualLimit = 100;
            }

            // 创建分页参数
            Pageable pageable = PageRequest.of(0, actualLimit);

            // 查询预警信息
            List<AlertResult> alertResults = alertResultRepository.findTopAlertsByPlanIdAndTimeRange(planId, startTime,
                    endTime, pageable);

            // 转换为DTO
            List<AlertListItemDTO> alertList = alertResults.stream()
                    .map(AlertListItemDTO::fromEntity)
                    .collect(Collectors.toList());

            log.info("Retrieved {} alerts for plan: {} in time range", alertList.size(), planId);
            return alertList;

        } catch (Exception e) {
            log.error("Error getting alert list for plan {} with time range: {}", planId, e.getMessage(), e);
            throw new RuntimeException("Failed to get alert list: " + e.getMessage(), e);
        }
    }

    @Override
    public List<AlertListItemDTO> getAlertListByEnterpriseId(String enterpriseId, Integer limit) {
        try {
            log.info("Getting alert list for enterprise: {}, limit: {}", enterpriseId, limit);

            // 验证参数
            if (enterpriseId == null || enterpriseId.trim().isEmpty()) {
                throw new IllegalArgumentException("Enterprise ID cannot be null or empty");
            }

            // 设置默认限制
            int actualLimit = (limit != null && limit > 0) ? limit : 20;

            // 验证限制范围
            if (actualLimit > 100) {
                log.warn("Limit {} exceeds maximum allowed (100), using 100 instead", actualLimit);
                actualLimit = 100;
            }

            // 创建分页参数
            Pageable pageable = PageRequest.of(0, actualLimit);

            // 查询预警信息
            List<AlertResult> alertResults = alertResultRepository.findTopAlertsByEnterpriseId(enterpriseId, pageable);

            // 转换为DTO
            List<AlertListItemDTO> alertList = alertResults.stream()
                    .map(AlertListItemDTO::fromEntity)
                    .collect(Collectors.toList());

            log.info("Retrieved {} alerts for enterprise: {}", alertList.size(), enterpriseId);
            return alertList;

        } catch (Exception e) {
            log.error("Error getting alert list for enterprise {}: {}", enterpriseId, e.getMessage(), e);
            throw new RuntimeException("Failed to get alert list: " + e.getMessage(), e);
        }
    }

    @Override
    public List<AlertListItemDTO> getAlertListByEnterpriseIdAndTimeRange(String enterpriseId, LocalDateTime startTime,
            LocalDateTime endTime, Integer limit) {
        try {
            log.info("Getting alert list for enterprise: {}, startTime: {}, endTime: {}, limit: {}", enterpriseId,
                    startTime, endTime, limit);

            // 验证参数
            if (enterpriseId == null || enterpriseId.trim().isEmpty()) {
                throw new IllegalArgumentException("Enterprise ID cannot be null or empty");
            }

            // 验证时间范围
            if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
                throw new IllegalArgumentException("Start time cannot be after end time");
            }

            // 设置默认限制
            int actualLimit = (limit != null && limit > 0) ? limit : 20;

            // 验证限制范围
            if (actualLimit > 100) {
                log.warn("Limit {} exceeds maximum allowed (100), using 100 instead", actualLimit);
                actualLimit = 100;
            }

            // 创建分页参数
            Pageable pageable = PageRequest.of(0, actualLimit);

            // 查询预警信息
            List<AlertResult> alertResults = alertResultRepository.findTopAlertsByEnterpriseIdAndTimeRange(enterpriseId,
                    startTime, endTime, pageable);

            // 转换为DTO
            List<AlertListItemDTO> alertList = alertResults.stream()
                    .map(AlertListItemDTO::fromEntity)
                    .collect(Collectors.toList());

            log.info("Retrieved {} alerts for enterprise: {} in time range", alertList.size(), enterpriseId);
            return alertList;

        } catch (Exception e) {
            log.error("Error getting alert list for enterprise {} with time range: {}", enterpriseId, e.getMessage(),
                    e);
            throw new RuntimeException("Failed to get alert list: " + e.getMessage(), e);
        }
    }

    @Override
    public List<AlertListItemDTO> getAlertListByPlanIdsAndTimeRange(List<Long> planIds, LocalDateTime startTime,
            LocalDateTime endTime, Integer limit) {
        try {
            log.info("Getting alert list for plans: {}, startTime: {}, endTime: {}, limit: {}", planIds, startTime,
                    endTime, limit);

            // 验证参数
            if (planIds == null || planIds.isEmpty()) {
                throw new IllegalArgumentException("Plan IDs list cannot be null or empty");
            }

            // 验证时间范围
            if (startTime != null && endTime != null && startTime.isAfter(endTime)) {
                throw new IllegalArgumentException("Start time cannot be after end time");
            }

            // 设置默认限制
            int actualLimit = (limit != null && limit > 0) ? limit : 20;

            // 验证限制范围
            if (actualLimit > 100) {
                log.warn("Limit {} exceeds maximum allowed (100), using 100 instead", actualLimit);
                actualLimit = 100;
            }

            // 创建分页参数
            Pageable pageable = PageRequest.of(0, actualLimit);

            // 查询预警信息
            List<AlertResult> alertResults = alertResultRepository.findTopAlertsByPlanIdsAndTimeRange(planIds,
                    startTime,
                    endTime, pageable);

            // 转换为DTO
            List<AlertListItemDTO> alertList = alertResults.stream()
                    .map(AlertListItemDTO::fromEntity)
                    .collect(Collectors.toList());

            log.info("Retrieved {} alerts for plans: {} in time range", alertList.size(), planIds);
            return alertList;

        } catch (Exception e) {
            log.error("Error getting alert list for plans {} with time range: {}", planIds, e.getMessage(), e);
            throw new RuntimeException("Failed to get alert list: " + e.getMessage(), e);
        }
    }

    @Override
    public AlertResultResponseDto getAlertById(Long alertId) {
        log.info("Getting alert by ID: {}", alertId);

        try {
            // 根据ID查询预警记录
            AlertResult alertResult = alertResultRepository.findById(alertId)
                    .orElseThrow(() -> new IllegalArgumentException("找不到指定的预警记录: " + alertId));

            // 转换为DTO
            AlertResultResponseDto responseDto = alertResultMapper.toResponseDto(alertResult);

            log.info("Successfully retrieved alert: {}", alertId);
            return responseDto;

        } catch (Exception e) {
            log.error("Error getting alert by ID {}: {}", alertId, e.getMessage(), e);
            throw new RuntimeException("Failed to get alert by ID: " + e.getMessage(), e);
        }
    }

    @Override
    public List<AlertResultResponseDto> getAlertsByIds(List<Long> alertIds) {
        log.info("Getting alerts by IDs: {}", alertIds);

        if (alertIds == null || alertIds.isEmpty()) {
            throw new IllegalArgumentException("预警ID列表不能为空");
        }

        try {
            // 根据ID列表查询预警记录
            List<AlertResult> alertResults = alertResultRepository.findAllById(alertIds);

            if (alertResults.isEmpty()) {
                log.warn("No alerts found for IDs: {}", alertIds);
                return new ArrayList<>();
            }

            // 转换为DTO列表
            List<AlertResultResponseDto> responseDtos = alertResults.stream()
                    .map(alertResultMapper::toResponseDto)
                    .collect(Collectors.toList());

            log.info("Successfully retrieved {} alerts for {} IDs", responseDtos.size(), alertIds.size());
            return responseDtos;

        } catch (Exception e) {
            log.error("Error getting alerts by IDs {}: {}", alertIds, e.getMessage(), e);
            throw new RuntimeException("Failed to get alerts by IDs: " + e.getMessage(), e);
        }
    }

}
