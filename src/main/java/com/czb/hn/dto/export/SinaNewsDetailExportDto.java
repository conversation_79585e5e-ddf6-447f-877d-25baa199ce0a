package com.czb.hn.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.czb.hn.dto.response.search.SinaNewsDetailResponseDto;
import com.czb.hn.enums.InformationSensitivityType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 新浪舆情详情导出DTO
 * 专门用于Excel导出，所有枚举字段都转换为中文描述
 */
public record SinaNewsDetailExportDto(
        @ExcelProperty("内容ID") @Schema(description = "内容ID") String contentId,

        @ExcelProperty("标题") @Schema(description = "标题") String title,

        @ExcelProperty("内容") @Schema(description = "内容") String content,

        @ExcelProperty("发布时间") @DateTimeFormat("yyyy-MM-dd HH:mm:ss") @Schema(description = "发布时间") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime publishTime,

        @ExcelProperty("来源") @Schema(description = "来源") String captureWebsite,

        @ExcelProperty("作者") @Schema(description = "作者") String author,

        @ExcelProperty("信息属性") @Schema(description = "信息属性") String sensitivityType,

        @ExcelProperty("涉及词") @Schema(description = "涉及词") String highLightWords,

        @ExcelProperty("相似文章数") @Schema(description = "相似文章数") Long similarityNum,

        @ExcelProperty("发布地") @Schema(description = "发布地") String publishProvince,

        @ExcelProperty("网页地址") @Schema(description = "网页地址") String url
) {

    /**
     * 从SinaNewsDetailResponseDto转换为导出DTO
     * 将所有枚举值转换为中文描述
     */
    public static SinaNewsDetailExportDto fromResponseDto(SinaNewsDetailResponseDto responseDto) {
        return new SinaNewsDetailExportDto(
                responseDto.getContentId(),
                responseDto.getTitle(),
                convertHighlightContent(responseDto.getHighlightContent()),
                responseDto.getPublishTime(),
                responseDto.getCaptureWebsite(),
                responseDto.getAuthor(),
                convertSensitivityType(responseDto.getSensitivityType()),
                convertHighLightWords(responseDto.getHighLightWords()),
                responseDto.getSimilarityNum(),
                responseDto.getPublishProvince(),
                responseDto.getUrl()
        );
    }

    /**
     * 转换高亮内容为字符串
     */
    private static String convertHighlightContent(Map<String, List<String>> highlightContent) {
        if (highlightContent == null || highlightContent.isEmpty()) {
            return "";
        }
        
        return highlightContent.entrySet().stream()
                .map(entry -> entry.getKey() + ": " + String.join(", ", entry.getValue()))
                .collect(Collectors.joining("; "));
    }

    /**
     * 转换信息敏感性类型
     */
    private static String convertSensitivityType(Integer value) {
        if (value == null) {
            return "";
        }
        for (InformationSensitivityType type : InformationSensitivityType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value.toString();
    }

    /**
     * 转换涉及词列表为字符串
     */
    private static String convertHighLightWords(List<Map<String, Integer>> highLightWords) {
        if (highLightWords == null || highLightWords.isEmpty()) {
            return "";
        }
        
        return highLightWords.stream()
                .map(wordMap -> wordMap.entrySet().stream()
                        .map(entry -> entry.getKey() + "(" + entry.getValue() + ")")
                        .collect(Collectors.joining(", ")))
                .collect(Collectors.joining("; "));
    }
}
