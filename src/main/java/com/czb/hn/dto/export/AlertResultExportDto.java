package com.czb.hn.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.enums.*;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 预警结果导出DTO
 * 专门用于Excel导出，所有枚举字段都转换为中文描述
 */
public record AlertResultExportDto(
        @ExcelProperty("预警ID") @Schema(description = "预警ID") Long id,

        @ExcelProperty("企业ID") @Schema(description = "企业ID") String enterpriseId,

        @ExcelProperty("方案ID") @Schema(description = "方案ID") Long planId,

        @ExcelProperty("配置ID") @Schema(description = "配置ID") Long configurationId,

        @ExcelProperty("标题") @Schema(description = "标题") String title,

        @ExcelProperty("正文") @Schema(description = "正文") String content,

        @ExcelProperty("涉及关键词") @Schema(description = "涉及关键词") String involvedKeywords,

        @ExcelProperty("信息敏感性类型") @Schema(description = "信息敏感性类型") String informationSensitivityType,

        @ExcelProperty("内容类别") @Schema(description = "内容类别") String contentCategory,

        @ExcelProperty("来源类型") @Schema(description = "来源类型") String sourceType,

        @ExcelProperty("内容类型") @Schema(description = "内容类型") String contentType,

        @ExcelProperty("内容匹配类型") @Schema(description = "内容匹配类型") String contentMatchType,

        @ExcelProperty("媒体级别") @Schema(description = "媒体级别") String mediaLevel,

        @ExcelProperty("预警级别") @Schema(description = "预警级别") String warningLevel,

        @ExcelProperty("来源") @Schema(description = "来源") String source,

        @ExcelProperty("省份") @Schema(description = "省份") String provincial,

        @ExcelProperty("预警时间") @DateTimeFormat("yyyy-MM-dd HH:mm:ss") @Schema(description = "预警时间") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime warningTime,

        @ExcelProperty("相似文章数") @Schema(description = "相似文章数") Integer similarArticleCount,

        @ExcelProperty("原始内容ID") @Schema(description = "原始内容ID") String originalContentId,

        @ExcelProperty("创建时间") @DateTimeFormat("yyyy-MM-dd HH:mm:ss") @Schema(description = "创建时间") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime createdAt,

        @ExcelProperty("更新时间") @DateTimeFormat("yyyy-MM-dd HH:mm:ss") @Schema(description = "更新时间") @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime updatedAt) {

    /**
     * 从AlertResultResponseDto转换为导出DTO
     * 将所有枚举值转换为中文描述
     */
    public static AlertResultExportDto fromResponseDto(AlertResultResponseDto responseDto) {
        return new AlertResultExportDto(
                responseDto.id(),
                responseDto.enterpriseId(),
                responseDto.planId(),
                responseDto.configurationId(),
                responseDto.title(),
                responseDto.content(),
                responseDto.involvedKeywords(),
                convertInformationSensitivityType(responseDto.informationSensitivityType()),
                convertContentCategory(responseDto.contentCategory()),
                convertSourceType(responseDto.sourceType()),
                convertContentType(responseDto.contentType()),
                convertContentMatchType(responseDto.contentMatchType()),
                convertMediaLevel(responseDto.mediaLevel()),
                convertWarningLevel(responseDto.warningLevel()),
                responseDto.source(),
                responseDto.provincial(),
                responseDto.warningTime(),
                responseDto.similarArticleCount(),
                responseDto.originalContentId(),
                responseDto.createdAt(),
                responseDto.updatedAt());
    }

    /**
     * 转换信息敏感性类型
     */
    private static String convertInformationSensitivityType(Integer value) {
        if (value == null) {
            return "";
        }
        for (InformationSensitivityType type : InformationSensitivityType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value.toString();
    }

    /**
     * 转换内容类别
     */
    private static String convertContentCategory(Integer value) {
        if (value == null) {
            return "";
        }
        for (ContentCategory category : ContentCategory.values()) {
            if (category.getValue().equals(value)) {
                return category.getDescription();
            }
        }
        return value.toString();
    }

    /**
     * 转换来源类型
     */
    private static String convertSourceType(String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }
        for (SourceType type : SourceType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value;
    }

    /**
     * 转换内容类型
     */
    private static String convertContentType(String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }
        for (ContentType type : ContentType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value;
    }

    /**
     * 转换内容匹配类型
     */
    private static String convertContentMatchType(String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }
        for (ContentMatchType type : ContentMatchType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value;
    }

    /**
     * 转换媒体级别
     */
    private static String convertMediaLevel(String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }
        for (MediaLevel level : MediaLevel.values()) {
            if (level.getValue().equals(value)) {
                return level.getDescription();
            }
        }
        return value;
    }

    /**
     * 转换预警级别
     */
    private static String convertWarningLevel(String value) {
        if (value == null || value.trim().isEmpty()) {
            return "";
        }
        try {
            AlertResult.WarningLevel warningLevel = AlertResult.WarningLevel.valueOf(value);
            return warningLevel.getDescription();
        } catch (IllegalArgumentException e) {
            return value;
        }
    }
}
