package com.czb.hn.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.enums.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 预警结果导出DTO
 * 专门用于Excel导出，所有枚举字段都转换为中文描述
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlertResultExportDto {

    @ExcelProperty("预警ID")
    private Long id;

    @ExcelProperty("企业ID")
    private String enterpriseId;

    @ExcelProperty("方案ID")
    private Long planId;

    @ExcelProperty("配置ID")
    private Long configurationId;

    @ExcelProperty("标题")
    private String title;

    @ExcelProperty("正文")
    private String content;

    @ExcelProperty("涉及关键词")
    private String involvedKeywords;

    @ExcelProperty("信息敏感性类型")
    private String informationSensitivityType;

    @ExcelProperty("内容类别")
    private String contentCategory;

    @ExcelProperty("来源类型")
    private String sourceType;

    @ExcelProperty("内容类型")
    private String contentType;

    @ExcelProperty("内容匹配类型")
    private String contentMatchType;

    @ExcelProperty("媒体级别")
    private String mediaLevel;

    @ExcelProperty("预警级别")
    private String warningLevel;

    @ExcelProperty("来源")
    private String source;

    @ExcelProperty("省份")
    private String provincial;

    @ExcelProperty("预警时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime warningTime;

    @ExcelProperty("相似文章数")
    private Integer similarArticleCount;

    @ExcelProperty("原始内容ID")
    private String originalContentId;

    @ExcelProperty("创建时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @ExcelProperty("更新时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 从AlertResultResponseDto转换为导出DTO
     * 将所有枚举值转换为中文描述
     */
    public static AlertResultExportDto fromResponseDto(AlertResultResponseDto responseDto) {
        AlertResultExportDto exportDto = new AlertResultExportDto();

        exportDto.setId(responseDto.id());
        exportDto.setEnterpriseId(responseDto.enterpriseId());
        exportDto.setPlanId(responseDto.planId());
        exportDto.setConfigurationId(responseDto.configurationId());
        exportDto.setTitle(responseDto.title());
        exportDto.setContent(responseDto.content());
        exportDto.setInvolvedKeywords(responseDto.involvedKeywords());

        // 转换枚举值为中文描述
        exportDto.setInformationSensitivityType(convertInformationSensitivityType(responseDto.informationSensitivityType()));
        exportDto.setContentCategory(convertContentCategory(responseDto.contentCategory()));
        exportDto.setSourceType(convertSourceType(responseDto.sourceType()));
        exportDto.setContentType(convertContentType(responseDto.contentType()));
        exportDto.setContentMatchType(convertContentMatchType(responseDto.contentMatchType()));
        exportDto.setMediaLevel(responseDto.mediaLevel()); // 已经是中文
        exportDto.setWarningLevel(convertWarningLevel(responseDto.warningLevel()));

        exportDto.setSource(responseDto.source());
        exportDto.setProvincial(responseDto.provincial());
        exportDto.setWarningTime(responseDto.warningTime());
        exportDto.setSimilarArticleCount(responseDto.similarArticleCount());
        exportDto.setOriginalContentId(responseDto.originalContentId());
        exportDto.setCreatedAt(responseDto.createdAt());
        exportDto.setUpdatedAt(responseDto.updatedAt());

        return exportDto;
    }

    /**
     * 转换信息敏感性类型
     */
    private static String convertInformationSensitivityType(Integer value) {
        if (value == null) {
            return "";
        }
        for (InformationSensitivityType type : InformationSensitivityType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value.toString();
    }

    /**
     * 转换内容类别
     */
    private static String convertContentCategory(Integer value) {
        if (value == null) {
            return "";
        }
        for (ContentCategory category : ContentCategory.values()) {
            if (category.getValue().equals(value)) {
                return category.getDescription();
            }
        }
        return value.toString();
    }

    /**
     * 转换来源类型
     */
    private static String convertSourceType(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }
        for (SourceType type : SourceType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value;
    }

    /**
     * 转换内容类型
     */
    private static String convertContentType(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }
        for (ContentType type : ContentType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value;
    }

    /**
     * 转换内容匹配类型
     */
    private static String convertContentMatchType(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }
        for (ContentMatchType type : ContentMatchType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value;
    }

    /**
     * 转换预警级别
     */
    private static String convertWarningLevel(String value) {
        if (value == null || value.isEmpty()) {
            return "";
        }
        // 直接映射预警级别
        switch (value.toUpperCase()) {
            case "SEVERE":
                return "严重";
            case "MEDIUM":
                return "中等";
            case "GENERAL":
                return "一般";
            default:
                return value;
        }
    }
}