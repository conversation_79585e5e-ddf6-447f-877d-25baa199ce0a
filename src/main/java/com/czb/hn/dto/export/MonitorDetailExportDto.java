package com.czb.hn.dto.export;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.czb.hn.dto.response.search.SinaNewsDetailResponseDto;
import com.czb.hn.enums.InformationSensitivityType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 监控详情导出DTO
 * 专门用于Excel导出，所有枚举字段都转换为中文描述
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitorDetailExportDto {
    @ExcelProperty("内容ID")
    private String contentId;

    @ExcelProperty("标题")
    private String title;

    @ExcelProperty("内容")
    private String content;

    @ExcelProperty("发布时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    @ExcelProperty("来源")
    private String captureWebsite;

    @ExcelProperty("作者")
    private String author;

    @ExcelProperty("信息属性")
    private String sensitivityType;

    @ExcelProperty("涉及词")
    private String highLightWords;

    @ExcelProperty("相似文章数")
    private Long similarityNum;

    @ExcelProperty("发布地")
    private String publishProvince;

    @ExcelProperty("网页地址")
    private String url;

    /**
     * 从SinaNewsDetailResponseDto转换为导出DTO
     * 将所有枚举值转换为中文描述
     */
    public static MonitorDetailExportDto fromResponseDto(SinaNewsDetailResponseDto responseDto) {
        MonitorDetailExportDto exportDto = new MonitorDetailExportDto();

        exportDto.setContentId(responseDto.getContentId());
        exportDto.setTitle(responseDto.getTitle());
        exportDto.setContent(convertHighlightContent(responseDto.getHighlightContent()));
        exportDto.setPublishTime(responseDto.getPublishTime());
        exportDto.setCaptureWebsite(responseDto.getCaptureWebsite());
        exportDto.setAuthor(responseDto.getAuthor());
        exportDto.setSensitivityType(convertSensitivityType(responseDto.getSensitivityType()));
        exportDto.setHighLightWords(convertHighLightWords(responseDto.getHighLightWords()));
        exportDto.setSimilarityNum(responseDto.getSimilarityNum());
        exportDto.setPublishProvince(responseDto.getPublishProvince());
        exportDto.setUrl(responseDto.getUrl());

        return exportDto;
    }

    /**
     * 转换高亮内容为字符串
     */
    private static String convertHighlightContent(Map<String, List<String>> highlightContent) {
        if (highlightContent == null || highlightContent.isEmpty()) {
            return "";
        }

        return highlightContent.entrySet().stream()
                .map(entry -> entry.getKey() + ": " + String.join(", ", entry.getValue()))
                .collect(Collectors.joining("; "));
    }

    /**
     * 转换信息敏感性类型
     */
    private static String convertSensitivityType(Integer value) {
        if (value == null) {
            return "";
        }
        for (InformationSensitivityType type : InformationSensitivityType.values()) {
            if (type.getValue().equals(value)) {
                return type.getDescription();
            }
        }
        return value.toString();
    }

    /**
     * 转换涉及词列表为字符串
     */
    private static String convertHighLightWords(List<Map<String, Integer>> highLightWords) {
        if (highLightWords == null || highLightWords.isEmpty()) {
            return "";
        }

        return highLightWords.stream()
                .map(wordMap -> wordMap.entrySet().stream()
                        .map(entry -> entry.getKey() + "(" + entry.getValue() + ")")
                        .collect(Collectors.joining(", ")))
                .collect(Collectors.joining("; "));
    }
}
