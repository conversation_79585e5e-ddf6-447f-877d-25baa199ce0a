package com.czb.hn.util;

import com.czb.hn.enums.*;
import com.czb.hn.enums.base.StandardEnum;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Excel导出工具类
 * 提供将对象数据导出为Excel文件的功能
 */
public class ExcelExportUtil {

    private static final Logger logger = LoggerFactory.getLogger(ExcelExportUtil.class);
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 导出单个对象到Excel
     *
     * @param data 要导出的对象
     * @param headers 列标题映射 (字段名 -> 显示名称)
     * @param sheetName 工作表名称
     * @return Excel文件的字节数组
     */
    public static byte[] exportSingleObject(Object data, Map<String, String> headers, String sheetName) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet(sheetName);
            
            // 创建标题样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            int rowIndex = 0;
            
            // 创建标题行
            Row headerRow = sheet.createRow(rowIndex++);
            headerRow.createCell(0).setCellValue("字段名");
            headerRow.createCell(1).setCellValue("字段值");
            headerRow.getCell(0).setCellStyle(headerStyle);
            headerRow.getCell(1).setCellStyle(headerStyle);
            
            // 获取对象的所有字段
            Field[] fields = data.getClass().getDeclaredFields();
            
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                
                // 跳过不需要导出的字段
                if (shouldSkipField(fieldName)) {
                    continue;
                }
                
                try {
                    Object value = field.get(data);
                    String displayName = headers.getOrDefault(fieldName, fieldName);
                    String displayValue = formatValueWithEnumConversion(value, fieldName);

                    Row dataRow = sheet.createRow(rowIndex++);
                    Cell nameCell = dataRow.createCell(0);
                    Cell valueCell = dataRow.createCell(1);

                    nameCell.setCellValue(displayName);
                    valueCell.setCellValue(displayValue);

                    nameCell.setCellStyle(dataStyle);
                    valueCell.setCellStyle(dataStyle);

                } catch (IllegalAccessException e) {
                    logger.warn("无法访问字段: {}", fieldName, e);
                }
            }
            
            // 自动调整列宽
            sheet.autoSizeColumn(0);
            sheet.autoSizeColumn(1);
            
            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            logger.error("导出Excel失败", e);
            throw new RuntimeException("导出Excel失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出对象列表到Excel
     *
     * @param dataList 要导出的对象列表
     * @param headers 列标题映射 (字段名 -> 显示名称)
     * @param sheetName 工作表名称
     * @return Excel文件的字节数组
     */
    public static byte[] exportObjectList(List<?> dataList, Map<String, String> headers, String sheetName) {
        if (dataList == null || dataList.isEmpty()) {
            throw new IllegalArgumentException("导出数据不能为空");
        }
        
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet(sheetName);
            
            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            Object firstObject = dataList.get(0);
            Field[] fields = firstObject.getClass().getDeclaredFields();
            
            // 过滤需要导出的字段
            List<Field> exportFields = Arrays.stream(fields)
                    .filter(field -> !shouldSkipField(field.getName()))
                    .toList();
            
            int rowIndex = 0;
            
            // 创建标题行
            Row headerRow = sheet.createRow(rowIndex++);
            for (int i = 0; i < exportFields.size(); i++) {
                Field field = exportFields.get(i);
                String displayName = headers.getOrDefault(field.getName(), field.getName());
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(displayName);
                cell.setCellStyle(headerStyle);
            }
            
            // 创建数据行
            for (Object data : dataList) {
                Row dataRow = sheet.createRow(rowIndex++);
                
                for (int i = 0; i < exportFields.size(); i++) {
                    Field field = exportFields.get(i);
                    field.setAccessible(true);
                    
                    try {
                        Object value = field.get(data);
                        String displayValue = formatValueWithEnumConversion(value, field.getName());

                        Cell cell = dataRow.createCell(i);
                        cell.setCellValue(displayValue);
                        cell.setCellStyle(dataStyle);

                    } catch (IllegalAccessException e) {
                        logger.warn("无法访问字段: {}", field.getName(), e);
                    }
                }
            }
            
            // 自动调整列宽
            for (int i = 0; i < exportFields.size(); i++) {
                sheet.autoSizeColumn(i);
            }
            
            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            logger.error("导出Excel失败", e);
            throw new RuntimeException("导出Excel失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建标题样式
     */
    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 创建数据样式
     */
    private static CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        return style;
    }

    /**
     * 格式化值为字符串，包含枚举值转换
     */
    private static String formatValueWithEnumConversion(Object value, String fieldName) {
        if (value == null) {
            return "";
        }

        // 特殊处理需要枚举转换的字段
        String convertedValue = convertEnumValueToDescription(value, fieldName);
        if (convertedValue != null) {
            return convertedValue;
        }

        return formatValue(value);
    }

    /**
     * 格式化值为字符串
     */
    private static String formatValue(Object value) {
        if (value == null) {
            return "";
        }

        if (value instanceof LocalDateTime) {
            return ((LocalDateTime) value).format(DATE_TIME_FORMATTER);
        }

        // 处理枚举值 - 显示中文描述而不是原始值
        if (value instanceof StandardEnum) {
            StandardEnum<?> enumValue = (StandardEnum<?>) value;
            return enumValue.getDescription();
        }

        // 处理其他枚举类型（如WarningLevel）
        if (value instanceof Enum) {
            Enum<?> enumValue = (Enum<?>) value;
            // 尝试调用getDescription方法
            try {
                java.lang.reflect.Method getDescriptionMethod = enumValue.getClass().getMethod("getDescription");
                Object description = getDescriptionMethod.invoke(enumValue);
                if (description != null) {
                    return description.toString();
                }
            } catch (Exception e) {
                // 如果没有getDescription方法，使用枚举名称
                logger.debug("Enum {} does not have getDescription method, using name", enumValue.getClass().getSimpleName());
            }
            return enumValue.name();
        }

        return value.toString();
    }

    /**
     * 将枚举值转换为描述文本
     * 专门处理DTO中存储的枚举value值
     */
    private static String convertEnumValueToDescription(Object value, String fieldName) {
        if (value == null) {
            return "";
        }

        try {
            switch (fieldName) {
                case "informationSensitivityType":
                case "sensitivityType":
                    if (value instanceof Integer) {
                        Integer intValue = (Integer) value;
                        for (InformationSensitivityType type : InformationSensitivityType.values()) {
                            if (type.getValue().equals(intValue)) {
                                return type.getDescription();
                            }
                        }
                    }
                    break;

                case "contentCategory":
                    if (value instanceof Integer) {
                        Integer intValue = (Integer) value;
                        for (ContentCategory category : ContentCategory.values()) {
                            if (category.getValue().equals(intValue)) {
                                return category.getDescription();
                            }
                        }
                    }
                    break;

                case "sourceType":
                    if (value instanceof String) {
                        String strValue = (String) value;
                        for (SourceType type : SourceType.values()) {
                            if (type.getValue().equals(strValue)) {
                                return type.getDescription();
                            }
                        }
                    }
                    break;

                case "contentType":
                    if (value instanceof String) {
                        String strValue = (String) value;
                        for (ContentType type : ContentType.values()) {
                            if (type.getValue().equals(strValue)) {
                                return type.getDescription();
                            }
                        }
                    }
                    break;

                case "mediaLevel":
                    if (value instanceof String) {
                        String strValue = (String) value;
                        for (MediaLevel level : MediaLevel.values()) {
                            if (level.getValue().equals(strValue)) {
                                return level.getDescription();
                            }
                        }
                    }
                    break;

                case "warningLevel":
                    if (value instanceof String) {
                        String strValue = (String) value;
                        try {
                            AlertResult.WarningLevel warningLevel = AlertResult.WarningLevel.valueOf(strValue);
                            return warningLevel.getDescription();
                        } catch (IllegalArgumentException e) {
                            logger.debug("Invalid warning level value: {}", strValue);
                        }
                    }
                    break;

                case "source":
                    // source字段通常是具体的来源名称，不需要枚举转换
                    return value.toString();

                default:
                    // 不是需要转换的字段，返回null让调用方使用默认格式化
                    return null;
            }
        } catch (Exception e) {
            logger.warn("Error converting enum value for field {}: {}", fieldName, e.getMessage());
        }

        // 如果转换失败，返回原始值
        return value.toString();
    }

    /**
     * 判断是否跳过某个字段
     */
    private static boolean shouldSkipField(String fieldName) {
        // 跳过一些不需要导出的字段
        return fieldName.equals("serialVersionUID") || 
               fieldName.startsWith("$") ||
               fieldName.equals("class");
    }

    /**
     * 生成带时间戳的文件名
     */
    public static String generateFileName(String prefix) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return prefix + "_" + timestamp + ".xlsx";
    }
}
