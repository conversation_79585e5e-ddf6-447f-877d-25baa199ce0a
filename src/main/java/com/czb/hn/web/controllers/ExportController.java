package com.czb.hn.web.controllers;

import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.dto.export.AlertResultExportDto;
import com.czb.hn.dto.export.SinaNewsDetailExportDto;
import com.czb.hn.dto.response.search.SinaNewsDetailResponseDto;
import com.czb.hn.util.EasyExcelExportUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 导出控制器
 * 提供各种数据的Excel导出功能
 */
@RestController
@RequestMapping("/api/export")
@Tag(name = "导出管理", description = "数据导出相关接口")
public class ExportController {

    @PostMapping("/alert-results")
    @Operation(summary = "导出预警结果", description = "将预警结果数据导出为Excel文件")
    public ResponseEntity<byte[]> exportAlertResults(
            @Parameter(description = "预警结果数据列表") @RequestBody List<AlertResultResponseDto> alertResults) {
        
        // 转换为导出DTO
        List<AlertResultExportDto> exportData = alertResults.stream()
                .map(AlertResultExportDto::fromResponseDto)
                .toList();

        // 使用EasyExcel导出
        byte[] excelData = EasyExcelExportUtil.exportToExcel(
                exportData, 
                AlertResultExportDto.class, 
                "预警结果"
        );

        // 生成文件名
        String fileName = "预警结果_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(excelData);
    }

    @PostMapping("/sina-news-details")
    @Operation(summary = "导出新浪舆情详情", description = "将新浪舆情详情数据导出为Excel文件")
    public ResponseEntity<byte[]> exportSinaNewsDetails(
            @Parameter(description = "新浪舆情详情数据列表") @RequestBody List<SinaNewsDetailResponseDto> newsDetails) {
        
        // 转换为导出DTO
        List<SinaNewsDetailExportDto> exportData = newsDetails.stream()
                .map(SinaNewsDetailExportDto::fromResponseDto)
                .toList();

        // 使用EasyExcel导出
        byte[] excelData = EasyExcelExportUtil.exportToExcel(
                exportData, 
                SinaNewsDetailExportDto.class, 
                "新浪舆情详情"
        );

        // 生成文件名
        String fileName = "新浪舆情详情_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(excelData);
    }

    @PostMapping("/combined-export")
    @Operation(summary = "组合导出", description = "将预警结果和新浪舆情详情数据导出到同一个Excel文件的不同工作表")
    public ResponseEntity<byte[]> exportCombinedData(
            @Parameter(description = "预警结果数据列表") @RequestParam(required = false) List<AlertResultResponseDto> alertResults,
            @Parameter(description = "新浪舆情详情数据列表") @RequestParam(required = false) List<SinaNewsDetailResponseDto> newsDetails) {
        
        List<EasyExcelExportUtil.ExportSheetData<?>> exportSheets = new java.util.ArrayList<>();

        // 添加预警结果工作表
        if (alertResults != null && !alertResults.isEmpty()) {
            List<AlertResultExportDto> alertExportData = alertResults.stream()
                    .map(AlertResultExportDto::fromResponseDto)
                    .toList();
            exportSheets.add(new EasyExcelExportUtil.ExportSheetData<>(
                    alertExportData, 
                    AlertResultExportDto.class, 
                    "预警结果"
            ));
        }

        // 添加新浪舆情详情工作表
        if (newsDetails != null && !newsDetails.isEmpty()) {
            List<SinaNewsDetailExportDto> newsExportData = newsDetails.stream()
                    .map(SinaNewsDetailExportDto::fromResponseDto)
                    .toList();
            exportSheets.add(new EasyExcelExportUtil.ExportSheetData<>(
                    newsExportData, 
                    SinaNewsDetailExportDto.class, 
                    "新浪舆情详情"
            ));
        }

        if (exportSheets.isEmpty()) {
            throw new IllegalArgumentException("至少需要提供一种类型的数据进行导出");
        }

        // 使用EasyExcel导出多工作表
        byte[] excelData = EasyExcelExportUtil.exportMultipleSheets(exportSheets);

        // 生成文件名
        String fileName = "综合数据导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(excelData);
    }
}
