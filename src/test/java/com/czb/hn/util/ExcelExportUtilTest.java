package com.czb.hn.util;

import com.czb.hn.enums.InformationSensitivityType;
import com.czb.hn.enums.SourceType;
import com.czb.hn.enums.ContentType;
import com.czb.hn.enums.MediaLevel;
import com.czb.hn.jpa.securadar.entity.AlertResult;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Excel导出工具类测试
 */
public class ExcelExportUtilTest {

    /**
     * 测试枚举值格式化
     */
    @Test
    public void testEnumFormatting() {
        // 创建测试数据对象
        TestDataObject testData = new TestDataObject();
        testData.setSensitivityType(InformationSensitivityType.SENSITIVE);
        testData.setSourceType(SourceType.WEIBO);
        testData.setContentType(ContentType.TEXT);
        testData.setMediaLevel(MediaLevel.NATIONAL);
        testData.setWarningLevel(AlertResult.WarningLevel.SEVERE);
        testData.setTitle("测试标题");
        testData.setCreateTime(LocalDateTime.of(2025, 7, 14, 14, 30, 0));

        // 定义列标题映射
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("title", "标题");
        headers.put("sensitivityType", "信息敏感性类型");
        headers.put("sourceType", "来源类型");
        headers.put("contentType", "内容类型");
        headers.put("mediaLevel", "媒体级别");
        headers.put("warningLevel", "预警级别");
        headers.put("createTime", "创建时间");

        // 导出Excel
        byte[] excelBytes = ExcelExportUtil.exportSingleObject(testData, headers, "测试数据");

        // 验证Excel文件不为空
        assertNotNull(excelBytes);
        assertTrue(excelBytes.length > 0);

        System.out.println("Excel文件大小: " + excelBytes.length + " bytes");
    }

    /**
     * 测试对象列表导出
     */
    @Test
    public void testObjectListExport() {
        // 创建测试数据列表
        TestDataObject data1 = new TestDataObject();
        data1.setSensitivityType(InformationSensitivityType.SENSITIVE);
        data1.setSourceType(SourceType.WEIBO);
        data1.setContentType(ContentType.TEXT);
        data1.setMediaLevel(MediaLevel.NATIONAL);
        data1.setWarningLevel(AlertResult.WarningLevel.SEVERE);
        data1.setTitle("测试标题1");
        data1.setCreateTime(LocalDateTime.of(2025, 7, 14, 14, 30, 0));

        TestDataObject data2 = new TestDataObject();
        data2.setSensitivityType(InformationSensitivityType.NON_SENSITIVE);
        data2.setSourceType(SourceType.WECHAT);
        data2.setContentType(ContentType.IMAGE);
        data2.setMediaLevel(MediaLevel.PROVINCIAL);
        data2.setWarningLevel(AlertResult.WarningLevel.MODERATE);
        data2.setTitle("测试标题2");
        data2.setCreateTime(LocalDateTime.of(2025, 7, 14, 15, 30, 0));

        List<TestDataObject> dataList = List.of(data1, data2);

        // 定义列标题映射
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("title", "标题");
        headers.put("sensitivityType", "信息敏感性类型");
        headers.put("sourceType", "来源类型");
        headers.put("contentType", "内容类型");
        headers.put("mediaLevel", "媒体级别");
        headers.put("warningLevel", "预警级别");
        headers.put("createTime", "创建时间");

        // 导出Excel
        byte[] excelBytes = ExcelExportUtil.exportObjectList(dataList, headers, "测试数据列表");

        // 验证Excel文件不为空
        assertNotNull(excelBytes);
        assertTrue(excelBytes.length > 0);

        System.out.println("Excel文件大小: " + excelBytes.length + " bytes");
    }

    /**
     * 测试数据对象
     */
    public static class TestDataObject {
        private String title;
        private InformationSensitivityType sensitivityType;
        private SourceType sourceType;
        private ContentType contentType;
        private MediaLevel mediaLevel;
        private AlertResult.WarningLevel warningLevel;
        private LocalDateTime createTime;

        // Getters and Setters
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }

        public InformationSensitivityType getSensitivityType() { return sensitivityType; }
        public void setSensitivityType(InformationSensitivityType sensitivityType) { this.sensitivityType = sensitivityType; }

        public SourceType getSourceType() { return sourceType; }
        public void setSourceType(SourceType sourceType) { this.sourceType = sourceType; }

        public ContentType getContentType() { return contentType; }
        public void setContentType(ContentType contentType) { this.contentType = contentType; }

        public MediaLevel getMediaLevel() { return mediaLevel; }
        public void setMediaLevel(MediaLevel mediaLevel) { this.mediaLevel = mediaLevel; }

        public AlertResult.WarningLevel getWarningLevel() { return warningLevel; }
        public void setWarningLevel(AlertResult.WarningLevel warningLevel) { this.warningLevel = warningLevel; }

        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
    }
}
