package com.czb.hn.dto.export;

import com.czb.hn.dto.alert.AlertResultResponseDto;
import com.czb.hn.dto.response.search.SinaNewsDetailResponseDto;
import com.czb.hn.util.EasyExcelExportUtil;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 导出DTO测试类
 */
class ExportDtoTest {

    @Test
    void testAlertResultExportDtoConversion() {
        // 创建测试数据
        AlertResultResponseDto responseDto = new AlertResultResponseDto(
                1L,
                "enterprise123",
                1L,
                1L,
                "测试标题",
                "测试内容",
                "[{\"keyword\":\"关键词1\",\"count\":3}]",
                1, // 敏感
                1, // 原创
                "wb", // 微博
                "1", // 文本
                "2", // 正文
                "央级",
                "SEVERE",
                "微博",
                "北京",
                LocalDateTime.now(),
                5,
                "content_123456",
                Map.of("key", "value"),
                LocalDateTime.now(),
                LocalDateTime.now()
        );

        // 转换为导出DTO
        AlertResultExportDto exportDto = AlertResultExportDto.fromResponseDto(responseDto);

        // 验证转换结果
        assertNotNull(exportDto);
        assertEquals(responseDto.id(), exportDto.id());
        assertEquals(responseDto.title(), exportDto.title());
        assertEquals("敏感", exportDto.informationSensitivityType());
        assertEquals("原创", exportDto.contentCategory());
        assertEquals("微博", exportDto.sourceType());
        assertEquals("文本", exportDto.contentType());
        assertEquals("正文", exportDto.contentMatchType());
        assertEquals("央级", exportDto.mediaLevel());
        assertEquals("严重", exportDto.warningLevel());
    }

    @Test
    void testSinaNewsDetailExportDtoConversion() {
        // 创建测试数据
        SinaNewsDetailResponseDto responseDto = new SinaNewsDetailResponseDto();
        responseDto.setContentId("content123");
        responseDto.setTitle("测试新闻标题");
        responseDto.setHighlightContent(Map.of("content", List.of("高亮内容1", "高亮内容2")));
        responseDto.setPublishTime(LocalDateTime.now());
        responseDto.setCaptureWebsite("新浪网");
        responseDto.setAuthor("测试作者");
        responseDto.setSensitivityType(1); // 敏感
        responseDto.setHighLightWords(List.of(Map.of("关键词1", 3), Map.of("关键词2", 2)));
        responseDto.setSimilarityNum(10L);
        responseDto.setPublishProvince("北京");
        responseDto.setUrl("http://example.com");

        // 转换为导出DTO
        SinaNewsDetailExportDto exportDto = SinaNewsDetailExportDto.fromResponseDto(responseDto);

        // 验证转换结果
        assertNotNull(exportDto);
        assertEquals(responseDto.getContentId(), exportDto.contentId());
        assertEquals(responseDto.getTitle(), exportDto.title());
        assertEquals("敏感", exportDto.sensitivityType());
        assertEquals("content: 高亮内容1, 高亮内容2", exportDto.content());
        assertEquals("关键词1(3); 关键词2(2)", exportDto.highLightWords());
        assertEquals(responseDto.getSimilarityNum(), exportDto.similarityNum());
    }

    @Test
    void testEasyExcelExportUtil() {
        // 创建测试数据
        AlertResultResponseDto responseDto = new AlertResultResponseDto(
                1L,
                "enterprise123",
                1L,
                1L,
                "测试标题",
                "测试内容",
                "[{\"keyword\":\"关键词1\",\"count\":3}]",
                1, // 敏感
                1, // 原创
                "wb", // 微博
                "1", // 文本
                "2", // 正文
                "央级",
                "SEVERE",
                "微博",
                "北京",
                LocalDateTime.now(),
                5,
                "content_123456",
                Map.of("key", "value"),
                LocalDateTime.now(),
                LocalDateTime.now()
        );

        AlertResultExportDto exportDto = AlertResultExportDto.fromResponseDto(responseDto);
        List<AlertResultExportDto> exportData = List.of(exportDto);

        // 测试导出功能
        byte[] excelData = EasyExcelExportUtil.exportToExcel(
                exportData, 
                AlertResultExportDto.class, 
                "测试工作表"
        );

        // 验证导出结果
        assertNotNull(excelData);
        assertTrue(excelData.length > 0);
    }

    @Test
    void testEasyExcelExportUtilWithEmptyData() {
        // 测试空数据导出
        assertThrows(IllegalArgumentException.class, () -> {
            EasyExcelExportUtil.exportToExcel(
                    List.of(), 
                    AlertResultExportDto.class, 
                    "测试工作表"
            );
        });

        assertThrows(IllegalArgumentException.class, () -> {
            EasyExcelExportUtil.exportToExcel(
                    null, 
                    AlertResultExportDto.class, 
                    "测试工作表"
            );
        });
    }
}
